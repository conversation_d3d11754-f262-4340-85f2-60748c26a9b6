import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import {
  depositTransactionDb,
  walletBalanceDb,
  transactionDb,
  adminSettingsDb,
  systemLogDb
} from '@/lib/database';
import { verifyUSDTTransaction, isValidTronTransactionId } from '@/lib/trongrid';
import { depositVerificationService } from '@/lib/depositVerificationService';

// Rate limiting map to prevent abuse
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 5; // 5 requests per minute per user

function checkRateLimit(userId: string): boolean {
  const now = Date.now();
  const userLimit = rateLimitMap.get(userId);

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(userId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }

  userLimit.count++;
  return true;
}

// POST - Verify deposit transaction
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check rate limiting
    if (!checkRateLimit(user.id)) {
      return NextResponse.json(
        { success: false, error: 'Too many verification requests. Please wait before trying again.' },
        { status: 429 }
      );
    }

    const body = await request.json();
    const { transactionId } = body;

    // Validation
    if (!transactionId || typeof transactionId !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Transaction ID is required' },
        { status: 400 }
      );
    }

    if (!isValidTronTransactionId(transactionId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid Tron transaction ID format' },
        { status: 400 }
      );
    }

    // Check if transaction already exists
    const existingDeposit = await depositTransactionDb.findByTransactionId(transactionId);
    if (existingDeposit) {
      await systemLogDb.create({
        action: 'DEPOSIT_DUPLICATE_ATTEMPT',
        userId: user.id,
        details: {
          transactionId,
          existingDepositId: existingDeposit.id,
          existingStatus: existingDeposit.status
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      });

      return NextResponse.json(
        {
          success: false,
          error: 'This transaction ID has already been submitted. Please check your deposit history.',
          data: {
            existingStatus: existingDeposit.status,
            submittedAt: existingDeposit.createdAt,
          }
        },
        { status: 400 }
      );
    }

    // Get deposit settings - try both camelCase and UPPER_CASE keys for compatibility
    let depositAddress = await adminSettingsDb.get('usdtDepositAddress');
    if (!depositAddress) {
      depositAddress = await adminSettingsDb.get('USDT_DEPOSIT_ADDRESS');
    }

    // Clean deposit address - remove quotes and extra characters
    if (depositAddress) {
      depositAddress = depositAddress.replace(/['"]/g, '').trim();
    }

    let minDepositAmount = await adminSettingsDb.get('minDepositAmount');
    if (!minDepositAmount) {
      minDepositAmount = await adminSettingsDb.get('MIN_DEPOSIT_AMOUNT');
    }
    minDepositAmount = parseFloat(minDepositAmount || '10');

    let maxDepositAmount = await adminSettingsDb.get('maxDepositAmount');
    if (!maxDepositAmount) {
      maxDepositAmount = await adminSettingsDb.get('MAX_DEPOSIT_AMOUNT');
    }
    maxDepositAmount = parseFloat(maxDepositAmount || '10000');

    let depositEnabled = await adminSettingsDb.get('depositEnabled');
    if (!depositEnabled) {
      depositEnabled = await adminSettingsDb.get('DEPOSIT_ENABLED');
    }
    depositEnabled = depositEnabled === 'true' || depositEnabled === true;

    let minConfirmations = await adminSettingsDb.get('minConfirmations');
    if (!minConfirmations) {
      minConfirmations = await adminSettingsDb.get('MIN_CONFIRMATIONS');
    }
    minConfirmations = parseInt(minConfirmations || '1');

    if (!depositEnabled) {
      return NextResponse.json(
        { success: false, error: 'Deposits are currently disabled' },
        { status: 503 }
      );
    }

    if (!depositAddress) {
      return NextResponse.json(
        { success: false, error: 'Deposit address not configured. Please contact support.' },
        { status: 503 }
      );
    }

    // Log verification attempt
    await systemLogDb.create({
      action: 'DEPOSIT_VERIFICATION_ATTEMPT',
      userId: user.id,
      details: { transactionId },
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    // Immediately create deposit record with PENDING_VERIFICATION status
    // This happens regardless of transaction validity
    const depositRecord = await depositTransactionDb.create({
      userId: user.id,
      transactionId,
      amount: 0, // Will be updated during verification
      usdtAmount: 0, // Will be updated during verification
      tronAddress: depositAddress,
      senderAddress: '', // Will be updated during verification
      blockNumber: '',
      blockTimestamp: new Date(),
      confirmations: 0,
    });

    // Update status to PENDING_VERIFICATION
    await depositTransactionDb.updateStatus(transactionId, 'PENDING_VERIFICATION');

    // Start background verification process with the current admin-configured deposit address
    await depositVerificationService.addTransactionForVerification(transactionId, depositAddress);

    // Log deposit submission
    await systemLogDb.create({
      action: 'DEPOSIT_SUBMITTED',
      userId: user.id,
      details: {
        transactionId,
        depositAddress,
        status: 'PENDING_VERIFICATION',
      },
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    // Return immediate success response
    return NextResponse.json({
      success: true,
      message: 'Deposit transaction submitted successfully. We are now verifying your transaction. This may take up to 2 minutes.',
      data: {
        transactionId,
        status: 'PENDING_VERIFICATION',
        estimatedVerificationTime: 'Within 2 minutes',
        nextSteps: [
          'Transaction verification in progress',
          'Confirmation checking will begin once transaction is found',
          `Wallet will be credited automatically after ${minConfirmations} confirmations`,
        ],
      },
    });

  } catch (error) {
    console.error('Deposit verification error:', error);
    
    // Log error for debugging
    if (request.headers.get('authorization')) {
      try {
        const { user } = await authenticateRequest(request);
        if (user) {
          await systemLogDb.create({
            action: 'DEPOSIT_VERIFICATION_ERROR',
            userId: user.id,
            details: { error: error instanceof Error ? error.message : 'Unknown error' },
            ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
          });
        }
      } catch (logError) {
        console.error('Failed to log error:', logError);
      }
    }

    return NextResponse.json(
      { success: false, error: 'Failed to verify deposit. Please try again later.' },
      { status: 500 }
    );
  }
}
