module.exports = {

"[project]/.next-internal/server/app/api/wallet/deposit/verify/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@opentelemetry/api", () => require("@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "adminSettingsDb": (()=>adminSettingsDb),
    "binaryPointsDb": (()=>binaryPointsDb),
    "depositTransactionDb": (()=>depositTransactionDb),
    "miningUnitDb": (()=>miningUnitDb),
    "referralDb": (()=>referralDb),
    "supportTicketDb": (()=>supportTicketDb),
    "systemLogDb": (()=>systemLogDb),
    "ticketResponseDb": (()=>ticketResponseDb),
    "transactionDb": (()=>transactionDb),
    "userDb": (()=>userDb),
    "walletBalanceDb": (()=>walletBalanceDb),
    "withdrawalDb": (()=>withdrawalDb)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
const userDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.create({
            data: {
                email: data.email,
                firstName: data.firstName,
                lastName: data.lastName,
                password: data.password,
                referralId: data.referralId || undefined
            }
        });
    },
    async findByEmail (email) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                email
            },
            include: {
                miningUnits: true,
                transactions: true,
                binaryPoints: true
            }
        });
    },
    async findById (id) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id
            },
            include: {
                miningUnits: true,
                transactions: true,
                binaryPoints: true
            }
        });
    },
    async findByReferralId (referralId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                referralId
            }
        });
    },
    async update (id, data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
            where: {
                id
            },
            data
        });
    },
    async updateKYCStatus (userId, status) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
            where: {
                id: userId
            },
            data: {
                kycStatus: status
            }
        });
    }
};
const miningUnitDb = {
    async create (data) {
        const expiryDate = new Date();
        expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.create({
            data: {
                userId: data.userId,
                thsAmount: data.thsAmount,
                investmentAmount: data.investmentAmount,
                dailyROI: data.dailyROI,
                expiryDate
            }
        });
    },
    async findActiveByUserId (userId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.findMany({
            where: {
                userId,
                status: 'ACTIVE',
                expiryDate: {
                    gt: new Date()
                }
            }
        });
    },
    async updateTotalEarned (unitId, amount) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.update({
            where: {
                id: unitId
            },
            data: {
                totalEarned: {
                    increment: amount
                }
            }
        });
    },
    async expireUnit (unitId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.update({
            where: {
                id: unitId
            },
            data: {
                status: 'EXPIRED'
            }
        });
    },
    async findAllActive () {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.findMany({
            where: {
                status: 'ACTIVE',
                expiryDate: {
                    gt: new Date()
                }
            },
            include: {
                user: true
            }
        });
    },
    async updateEarnings (unitId, earningType, amount) {
        const updateData = {
            totalEarned: {
                increment: amount
            }
        };
        switch(earningType){
            case 'mining':
                updateData.miningEarnings = {
                    increment: amount
                };
                break;
            case 'referral':
                updateData.referralEarnings = {
                    increment: amount
                };
                break;
            case 'binary':
                updateData.binaryEarnings = {
                    increment: amount
                };
                break;
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.update({
            where: {
                id: unitId
            },
            data: updateData
        });
    }
};
const transactionDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.create({
            data: {
                userId: data.userId,
                type: data.type,
                amount: data.amount,
                description: data.description,
                reference: data.reference,
                status: data.status || 'PENDING'
            }
        });
    },
    async findByUserId (userId, filters) {
        const where = {
            userId
        };
        if (filters?.types && filters.types.length > 0) {
            where.type = {
                in: filters.types
            };
        }
        if (filters?.status) {
            where.status = filters.status;
        }
        if (filters?.search) {
            where.OR = [
                {
                    description: {
                        contains: filters.search,
                        mode: 'insensitive'
                    }
                },
                {
                    type: {
                        contains: filters.search,
                        mode: 'insensitive'
                    }
                },
                {
                    reference: {
                        contains: filters.search,
                        mode: 'insensitive'
                    }
                }
            ];
        }
        const include = filters?.includeUser ? {
            user: {
                select: {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true
                }
            }
        } : undefined;
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.findMany({
            where,
            include,
            orderBy: {
                createdAt: 'desc'
            },
            take: filters?.limit || 50,
            skip: filters?.offset
        });
    },
    async updateStatus (transactionId, status) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.update({
            where: {
                id: transactionId
            },
            data: {
                status
            }
        });
    }
};
const referralDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.create({
            data: {
                referrerId: data.referrerId,
                referredId: data.referredId,
                placementSide: data.placementSide
            }
        });
    },
    async findByReferrerId (referrerId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findMany({
            where: {
                referrerId
            },
            include: {
                referred: {
                    select: {
                        id: true,
                        email: true,
                        createdAt: true
                    }
                }
            }
        });
    }
};
const binaryPointsDb = {
    async upsert (data) {
        // Round to 2 decimal places to ensure precision
        const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;
        const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].binaryPoints.upsert({
            where: {
                userId: data.userId
            },
            update: {
                leftPoints: leftPoints !== undefined ? {
                    increment: leftPoints
                } : undefined,
                rightPoints: rightPoints !== undefined ? {
                    increment: rightPoints
                } : undefined
            },
            create: {
                userId: data.userId,
                leftPoints: leftPoints || 0,
                rightPoints: rightPoints || 0
            }
        });
    },
    async findByUserId (userId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].binaryPoints.findUnique({
            where: {
                userId
            }
        });
    },
    async resetPoints (userId, leftPoints, rightPoints) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].binaryPoints.update({
            where: {
                userId
            },
            data: {
                leftPoints,
                rightPoints,
                flushDate: new Date()
            }
        });
    }
};
const withdrawalDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].withdrawalRequest.create({
            data: {
                userId: data.userId,
                amount: data.amount,
                usdtAddress: data.usdtAddress
            }
        });
    },
    async findPending () {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].withdrawalRequest.findMany({
            where: {
                status: 'PENDING'
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        kycStatus: true
                    }
                }
            },
            orderBy: {
                createdAt: 'asc'
            }
        });
    },
    async updateStatus (requestId, status, processedBy, txid, rejectionReason) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].withdrawalRequest.update({
            where: {
                id: requestId
            },
            data: {
                status,
                processedBy,
                txid,
                rejectionReason,
                processedAt: new Date()
            }
        });
    }
};
const adminSettingsDb = {
    async get (key) {
        const setting = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].adminSettings.findUnique({
            where: {
                key
            }
        });
        return setting?.value;
    },
    async set (key, value, updatedBy) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].adminSettings.upsert({
            where: {
                key
            },
            update: {
                value
            },
            create: {
                key,
                value
            }
        });
    },
    async getAll () {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].adminSettings.findMany();
    }
};
const systemLogDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].systemLog.create({
            data: {
                action: data.action,
                userId: data.userId,
                adminId: data.adminId,
                details: data.details ? JSON.stringify(data.details) : null,
                ipAddress: data.ipAddress,
                userAgent: data.userAgent
            }
        });
    }
};
const walletBalanceDb = {
    async getOrCreate (userId) {
        let walletBalance = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.findUnique({
            where: {
                userId
            }
        });
        if (!walletBalance) {
            walletBalance = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.create({
                data: {
                    userId,
                    availableBalance: 0,
                    pendingBalance: 0,
                    totalDeposits: 0,
                    totalWithdrawals: 0,
                    totalEarnings: 0
                }
            });
        }
        return walletBalance;
    },
    async updateBalance (userId, updates) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.update({
            where: {
                userId
            },
            data: {
                ...updates,
                lastUpdated: new Date()
            }
        });
    },
    async addDeposit (userId, amount) {
        const wallet = await this.getOrCreate(userId);
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.update({
            where: {
                userId
            },
            data: {
                availableBalance: wallet.availableBalance + amount,
                totalDeposits: wallet.totalDeposits + amount,
                lastUpdated: new Date()
            }
        });
    },
    async addEarnings (userId, amount) {
        const wallet = await this.getOrCreate(userId);
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.update({
            where: {
                userId
            },
            data: {
                availableBalance: wallet.availableBalance + amount,
                totalEarnings: wallet.totalEarnings + amount,
                lastUpdated: new Date()
            }
        });
    },
    async deductWithdrawal (userId, amount) {
        const wallet = await this.getOrCreate(userId);
        if (wallet.availableBalance < amount) {
            throw new Error('Insufficient balance');
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.update({
            where: {
                userId
            },
            data: {
                availableBalance: wallet.availableBalance - amount,
                totalWithdrawals: wallet.totalWithdrawals + amount,
                lastUpdated: new Date()
            }
        });
    },
    async findByUserId (userId) {
        return await this.getOrCreate(userId);
    }
};
const depositTransactionDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.create({
            data: {
                userId: data.userId,
                transactionId: data.transactionId,
                amount: data.amount,
                usdtAmount: data.usdtAmount,
                tronAddress: data.tronAddress,
                senderAddress: data.senderAddress,
                blockNumber: data.blockNumber,
                blockTimestamp: data.blockTimestamp,
                confirmations: data.confirmations || 0,
                status: 'PENDING'
            }
        });
    },
    async findByTransactionId (transactionId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.findUnique({
            where: {
                transactionId
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            }
        });
    },
    async findByUserId (userId, filters) {
        const where = {
            userId
        };
        if (filters?.status) {
            where.status = filters.status;
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.findMany({
            where,
            orderBy: {
                createdAt: 'desc'
            },
            take: filters?.limit || 50,
            skip: filters?.offset,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            }
        });
    },
    async findAll (filters) {
        const where = {};
        if (filters?.status) {
            where.status = filters.status;
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.findMany({
            where,
            orderBy: {
                createdAt: 'desc'
            },
            take: filters?.limit || 100,
            skip: filters?.offset,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            }
        });
    },
    async updateStatus (transactionId, status, updates) {
        const updateData = {
            status
        };
        if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;
        if (updates?.processedAt) updateData.processedAt = updates.processedAt;
        if (updates?.failureReason) updateData.failureReason = updates.failureReason;
        if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.update({
            where: {
                transactionId
            },
            data: updateData
        });
    },
    async markAsCompleted (transactionId) {
        return await this.updateStatus(transactionId, 'COMPLETED', {
            processedAt: new Date()
        });
    },
    async markAsFailed (transactionId, reason) {
        return await this.updateStatus(transactionId, 'FAILED', {
            failureReason: reason,
            processedAt: new Date()
        });
    },
    async getPendingDeposits () {
        return await this.findAll({
            status: 'PENDING'
        });
    },
    async getPendingVerificationDeposits () {
        return await this.findAll({
            status: 'PENDING_VERIFICATION'
        });
    },
    async getWaitingForConfirmationsDeposits () {
        return await this.findAll({
            status: 'WAITING_FOR_CONFIRMATIONS'
        });
    },
    async findByStatus (status) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.findMany({
            where: {
                status
            },
            orderBy: {
                createdAt: 'desc'
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            }
        });
    },
    async updateConfirmations (transactionId, confirmations) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.update({
            where: {
                transactionId
            },
            data: {
                confirmations
            }
        });
    },
    async getDepositStats () {
        const stats = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.aggregate({
            _count: {
                id: true
            },
            _sum: {
                usdtAmount: true
            },
            where: {
                status: {
                    in: [
                        'COMPLETED',
                        'CONFIRMED'
                    ]
                }
            }
        });
        const pendingCount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.count({
            where: {
                status: {
                    in: [
                        'PENDING',
                        'PENDING_VERIFICATION',
                        'WAITING_FOR_CONFIRMATIONS'
                    ]
                }
            }
        });
        return {
            totalDeposits: stats._count.id || 0,
            totalAmount: stats._sum.usdtAmount || 0,
            pendingDeposits: pendingCount
        };
    }
};
const supportTicketDb = {
    create: async (data)=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].supportTicket.create({
            data,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                },
                responses: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true,
                                firstName: true,
                                lastName: true
                            }
                        }
                    },
                    orderBy: {
                        createdAt: 'asc'
                    }
                }
            }
        });
    },
    findByUserId: async (userId)=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].supportTicket.findMany({
            where: {
                userId
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                },
                responses: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true,
                                firstName: true,
                                lastName: true
                            }
                        }
                    },
                    orderBy: {
                        createdAt: 'asc'
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
    },
    findById: async (id)=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].supportTicket.findUnique({
            where: {
                id
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                },
                responses: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true,
                                firstName: true,
                                lastName: true
                            }
                        }
                    },
                    orderBy: {
                        createdAt: 'asc'
                    }
                }
            }
        });
    },
    findAll: async ()=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].supportTicket.findMany({
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                },
                responses: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true,
                                firstName: true,
                                lastName: true
                            }
                        }
                    },
                    orderBy: {
                        createdAt: 'asc'
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
    },
    updateStatus: async (id, status)=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].supportTicket.update({
            where: {
                id
            },
            data: {
                status,
                updatedAt: new Date()
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                },
                responses: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true,
                                firstName: true,
                                lastName: true
                            }
                        }
                    },
                    orderBy: {
                        createdAt: 'asc'
                    }
                }
            }
        });
    }
};
const ticketResponseDb = {
    create: async (data)=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].ticketResponse.create({
            data,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            }
        });
    },
    findByTicketId: async (ticketId)=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].ticketResponse.findMany({
            where: {
                ticketId
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            },
            orderBy: {
                createdAt: 'asc'
            }
        });
    }
};
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authenticateRequest": (()=>authenticateRequest),
    "createSession": (()=>createSession),
    "generateReferralId": (()=>generateReferralId),
    "generateToken": (()=>generateToken),
    "hashPassword": (()=>hashPassword),
    "isAdmin": (()=>isAdmin),
    "loginUser": (()=>loginUser),
    "registerUser": (()=>registerUser),
    "validateEmail": (()=>validateEmail),
    "validatePassword": (()=>validatePassword),
    "validateSession": (()=>validateSession),
    "verifyPassword": (()=>verifyPassword),
    "verifyToken": (()=>verifyToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
;
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
const hashPassword = async (password)=>{
    return await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(password, 12);
};
const verifyPassword = async (password, hashedPassword)=>{
    return await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, hashedPassword);
};
const generateToken = (payload)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign(payload, JWT_SECRET, {
        expiresIn: JWT_EXPIRES_IN
    });
};
const verifyToken = (token)=>{
    try {
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, JWT_SECRET);
        return decoded;
    } catch (error) {
        return null;
    }
};
const generateReferralId = ()=>{
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = 'HC'; // HashCoreX prefix
    for(let i = 0; i < 8; i++){
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
};
const authenticateRequest = async (request)=>{
    const token = request.headers.get('authorization')?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;
    if (!token) {
        return {
            authenticated: false,
            user: null
        };
    }
    const decoded = verifyToken(token);
    if (!decoded) {
        return {
            authenticated: false,
            user: null
        };
    }
    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByEmail(decoded.email);
    if (!user) {
        return {
            authenticated: false,
            user: null
        };
    }
    return {
        authenticated: true,
        user
    };
};
const registerUser = async (data)=>{
    // Check if user already exists
    const existingUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByEmail(data.email);
    if (existingUser) {
        throw new Error('User already exists with this email');
    }
    // Validate referral code if provided
    let referrerId;
    if (data.referralCode) {
        const referrer = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByReferralId(data.referralCode);
        if (!referrer) {
            throw new Error('Invalid referral code');
        }
        referrerId = referrer.id;
    }
    // Hash password
    const passwordHash = await hashPassword(data.password);
    // Generate unique referral ID
    let referralId;
    let isUnique = false;
    do {
        referralId = generateReferralId();
        const existing = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByReferralId(referralId);
        isUnique = !existing;
    }while (!isUnique)
    // Create user in PostgreSQL
    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].create({
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        password: passwordHash,
        referralId
    });
    // Create referral relationship if referrer exists
    if (referrerId) {
        const { placeUserByReferralType } = await __turbopack_context__.r("[project]/src/lib/referral.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        // Determine referral type based on placementSide parameter
        let referralType = 'general';
        if (data.placementSide === 'left') {
            referralType = 'left';
        } else if (data.placementSide === 'right') {
            referralType = 'right';
        }
        // Place user using the new unified placement function
        await placeUserByReferralType(referrerId, user.id, referralType);
    }
    return {
        id: user.id,
        email: user.email,
        referralId: user.referralId,
        kycStatus: user.kycStatus
    };
};
const loginUser = async (data)=>{
    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByEmail(data.email);
    if (!user) {
        throw new Error('Invalid email or password');
    }
    const isValidPassword = await verifyPassword(data.password, user.password);
    if (!isValidPassword) {
        throw new Error('Invalid email or password');
    }
    const token = generateToken({
        userId: user.id,
        email: user.email
    });
    return {
        token,
        user: {
            id: user.id,
            email: user.email,
            referralId: user.referralId,
            kycStatus: user.kycStatus
        }
    };
};
const validatePassword = (password)=>{
    const errors = [];
    if (password.length < 8) {
        errors.push('Password must be at least 8 characters long');
    }
    if (!/[A-Z]/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
    }
    if (!/[a-z]/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
    }
    if (!/\d/.test(password)) {
        errors.push('Password must contain at least one number');
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        errors.push('Password must contain at least one special character');
    }
    return {
        valid: errors.length === 0,
        errors
    };
};
const validateEmail = (email)=>{
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};
const createSession = (userId, email)=>{
    return generateToken({
        userId,
        email
    });
};
const validateSession = (token)=>{
    return verifyToken(token);
};
const isAdmin = async (userId)=>{
    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findById(userId);
    return user?.role === 'ADMIN';
};
}}),
"[project]/src/lib/trongrid.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Trongrid API Integration for USDT TRC20 Transaction Verification
 *
 * This module provides utilities to interact with the Trongrid API
 * to verify USDT TRC20 transactions on the Tron blockchain.
 * Supports both Mainnet and Testnet configurations.
 */ __turbopack_context__.s({
    "getAccountInfo": (()=>getAccountInfo),
    "getCurrentBlock": (()=>getCurrentBlock),
    "getCurrentNetworkConfig": (()=>getCurrentNetworkConfig),
    "getTransactionById": (()=>getTransactionById),
    "getTransactionInfo": (()=>getTransactionInfo),
    "isValidTronAddress": (()=>isValidTronAddress),
    "isValidTronTransactionId": (()=>isValidTronTransactionId),
    "verifyUSDTTransaction": (()=>verifyUSDTTransaction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tron$2d$format$2d$address$2f$build$2f$lib$2f$crypto$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tron-format-address/build/lib/crypto.js [app-route] (ecmascript)");
;
;
const TRONGRID_API_KEY = process.env.TRONGRID_API_KEY; // Optional, for higher rate limits
// Rate limiting configuration
const RATE_LIMIT_DELAY = 1000; // 1 second between requests
let lastRequestTime = 0;
/**
 * Get current Tron network configuration from admin settings
 */ async function getTronNetworkConfig() {
    try {
        const network = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('tronNetwork') || 'testnet';
        let mainnetApiUrl = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('tronMainnetApiUrl') || 'https://api.trongrid.io';
        let testnetApiUrl = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('tronTestnetApiUrl') || 'https://api.shasta.trongrid.io';
        let mainnetContract = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('usdtMainnetContract') || 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
        let testnetContract = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('usdtTestnetContract') || 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs';
        // Clean up URLs and contracts - remove quotes and extra characters
        mainnetApiUrl = mainnetApiUrl.replace(/['"]/g, '').trim();
        testnetApiUrl = testnetApiUrl.replace(/['"]/g, '').trim();
        mainnetContract = mainnetContract.replace(/['"]/g, '').trim();
        testnetContract = testnetContract.replace(/['"]/g, '').trim();
        // Ensure URLs don't end with slash
        mainnetApiUrl = mainnetApiUrl.replace(/\/$/, '');
        testnetApiUrl = testnetApiUrl.replace(/\/$/, '');
        const isMainnet = network === 'mainnet';
        const finalApiUrl = isMainnet ? mainnetApiUrl : testnetApiUrl;
        const finalContract = isMainnet ? mainnetContract : testnetContract;
        console.log('Tron network config:', {
            network,
            apiUrl: finalApiUrl,
            usdtContract: finalContract
        });
        return {
            apiUrl: finalApiUrl,
            usdtContract: finalContract,
            network: network
        };
    } catch (error) {
        console.error('Error getting Tron network config, using testnet defaults:', error);
        // Fallback to testnet configuration
        return {
            apiUrl: 'https://api.shasta.trongrid.io',
            usdtContract: 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs',
            network: 'testnet'
        };
    }
}
/**
 * Rate limiting helper to prevent API abuse
 */ async function rateLimitDelay() {
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
        const delay = RATE_LIMIT_DELAY - timeSinceLastRequest;
        await new Promise((resolve)=>setTimeout(resolve, delay));
    }
    lastRequestTime = Date.now();
}
/**
 * Make HTTP request to Trongrid API with proper headers and error handling
 */ async function makeApiRequest(endpoint, networkConfig) {
    await rateLimitDelay();
    // Get network config if not provided
    if (!networkConfig) {
        networkConfig = await getTronNetworkConfig();
    }
    const headers = {
        'Content-Type': 'application/json'
    };
    if (TRONGRID_API_KEY) {
        headers['TRON-PRO-API-KEY'] = TRONGRID_API_KEY;
    }
    const response = await fetch(`${networkConfig.apiUrl}${endpoint}`, {
        method: 'GET',
        headers
    });
    if (!response.ok) {
        throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);
    }
    return await response.json();
}
async function getTransactionById(txId, networkConfig) {
    try {
        const config = networkConfig || await getTronNetworkConfig();
        const url = `${config.apiUrl}/walletsolidity/gettransactionbyid`;
        console.log('Fetching transaction by ID:', {
            txId,
            url,
            network: config.network
        });
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                value: txId
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Trongrid API error response:', {
                status: response.status,
                statusText: response.statusText,
                body: errorText
            });
            throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        console.log('Transaction data received:', data);
        return data.txID ? data : null;
    } catch (error) {
        console.error('Error fetching transaction:', {
            txId,
            error: error instanceof Error ? error.message : error,
            stack: error instanceof Error ? error.stack : undefined
        });
        return null;
    }
}
async function getTransactionInfo(txId, networkConfig) {
    try {
        const config = networkConfig || await getTronNetworkConfig();
        const url = `${config.apiUrl}/walletsolidity/gettransactioninfobyid`;
        console.log('Fetching transaction info:', {
            txId,
            url,
            network: config.network
        });
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                value: txId
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Trongrid API error response:', {
                status: response.status,
                statusText: response.statusText,
                body: errorText
            });
            throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        console.log('Transaction info received:', data);
        return data.id ? data : null;
    } catch (error) {
        console.error('Error fetching transaction info:', {
            txId,
            error: error instanceof Error ? error.message : error,
            stack: error instanceof Error ? error.stack : undefined
        });
        return null;
    }
}
async function getCurrentBlock(networkConfig) {
    try {
        const config = networkConfig || await getTronNetworkConfig();
        const url = `${config.apiUrl}/walletsolidity/getnowblock`;
        console.log('Fetching current block:', {
            url,
            network: config.network
        });
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Trongrid API error response:', {
                status: response.status,
                statusText: response.statusText,
                body: errorText
            });
            throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        console.log('Current block data received:', data);
        return data.block_header ? {
            blockNumber: data.block_header.raw_data.number
        } : null;
    } catch (error) {
        console.error('Error fetching current block:', {
            error: error instanceof Error ? error.message : error,
            stack: error instanceof Error ? error.stack : undefined
        });
        return null;
    }
}
/**
 * Convert hex string to decimal number
 */ function hexToDecimal(hex) {
    return parseInt(hex, 16);
}
/**
 * Convert Tron address from hex to base58
 */ function hexToTronAddress(hex) {
    try {
        // Remove '0x' prefix if present
        let cleanHex = hex.startsWith('0x') ? hex.slice(2) : hex;
        // Ensure we have a 40-character hex string (20 bytes)
        if (cleanHex.length < 40) {
            cleanHex = '0'.repeat(40 - cleanHex.length) + cleanHex;
        }
        // Add the Tron address prefix (0x41 for mainnet/testnet)
        const addressHex = '41' + cleanHex;
        // Convert to base58
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tron$2d$format$2d$address$2f$build$2f$lib$2f$crypto$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fromHex"])(addressHex);
    } catch (error) {
        console.error('Error converting hex to Tron address:', error);
        // Fallback to a recognizable format if conversion fails
        return `T${hex.slice(-30)}`;
    }
}
/**
 * Parse USDT TRC20 transfer from transaction data and logs
 */ function parseUSDTTransfer(transaction, logs, usdtContract) {
    console.log('Parsing USDT transfer from transaction and logs:', logs.length, 'Contract:', usdtContract);
    // First, try to parse from transaction data (more reliable for recipient address)
    if (transaction?.raw_data?.contract?.[0]?.parameter?.value) {
        const contractValue = transaction.raw_data.contract[0].parameter.value;
        // Check if this is a USDT contract call
        const contractAddressBase58 = hexToTronAddress(contractValue.contract_address?.replace('41', '') || '');
        const isUSDTContract = contractAddressBase58.toLowerCase() === usdtContract.toLowerCase();
        if (isUSDTContract && contractValue.data) {
            try {
                const data = contractValue.data;
                // Check if this is a transfer function call (a9059cbb = transfer)
                if (data.startsWith('a9059cbb')) {
                    console.log('Found USDT transfer in transaction data');
                    // Parse recipient address from data (next 64 chars after function selector)
                    const recipientHex = data.slice(8, 72).slice(24); // Remove padding
                    const toAddress = hexToTronAddress(recipientHex);
                    // Parse amount from data (last 64 chars)
                    const amountHex = data.slice(72);
                    const amount = hexToDecimal(amountHex) / 1000000; // USDT has 6 decimals
                    // Get sender address from transaction
                    const fromAddress = hexToTronAddress(contractValue.owner_address?.replace('41', '') || '');
                    console.log('Parsed transfer from transaction data:', {
                        fromAddress,
                        toAddress,
                        amount,
                        recipientHex,
                        amountHex
                    });
                    return {
                        amount,
                        fromAddress,
                        toAddress
                    };
                }
            } catch (error) {
                console.error('Error parsing transaction data:', error);
            }
        }
    }
    // Fallback to parsing from logs if transaction data parsing fails
    const usdtLog = logs.find((log)=>{
        const logAddressBase58 = hexToTronAddress(log.address);
        const isUSDTContract = logAddressBase58.toLowerCase() === usdtContract.toLowerCase();
        const hasCorrectTopics = log.topics.length >= 3;
        const isTransferEvent = log.topics[0] === 'ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef';
        return isUSDTContract && hasCorrectTopics && isTransferEvent;
    });
    if (!usdtLog) {
        console.log('No USDT transfer found in transaction data or logs');
        return null;
    }
    try {
        console.log('Parsing from logs as fallback:', usdtLog);
        const fromAddressHex = usdtLog.topics[1].slice(26);
        const toAddressHex = usdtLog.topics[2].slice(26);
        const amountHex = usdtLog.data.startsWith('0x') ? usdtLog.data.slice(2) : usdtLog.data;
        const fromAddress = hexToTronAddress(fromAddressHex);
        const toAddress = hexToTronAddress(toAddressHex);
        const amount = hexToDecimal(amountHex) / 1000000;
        return {
            amount,
            fromAddress,
            toAddress
        };
    } catch (error) {
        console.error('Error parsing USDT transfer from logs:', error);
        return null;
    }
}
async function verifyUSDTTransaction(txId, expectedToAddress, minConfirmations = 1) {
    // Get current network configuration
    const networkConfig = await getTronNetworkConfig();
    console.log('Verifying USDT transaction:', {
        txId,
        expectedToAddress,
        minConfirmations,
        network: networkConfig.network,
        apiUrl: networkConfig.apiUrl,
        usdtContract: networkConfig.usdtContract
    });
    try {
        // Get transaction details
        const transaction = await getTransactionById(txId, networkConfig);
        console.log('Transaction details:', transaction);
        if (!transaction) {
            console.log('Transaction not found');
            return {
                isValid: false,
                amount: 0,
                fromAddress: '',
                toAddress: '',
                contractAddress: networkConfig.usdtContract,
                blockNumber: 0,
                blockTimestamp: 0,
                confirmations: 0,
                transactionId: txId
            };
        }
        // Get transaction info for receipt and confirmations
        const transactionInfo = await getTransactionInfo(txId, networkConfig);
        console.log('Transaction info:', transactionInfo);
        if (!transactionInfo) {
            console.log('Transaction info not found');
            return {
                isValid: false,
                amount: 0,
                fromAddress: '',
                toAddress: '',
                contractAddress: networkConfig.usdtContract,
                blockNumber: 0,
                blockTimestamp: 0,
                confirmations: 0,
                transactionId: txId
            };
        }
        // Check if transaction was successful
        console.log('Transaction receipt result:', transactionInfo.receipt?.result);
        if (transactionInfo.receipt?.result !== 'SUCCESS') {
            console.log('Transaction failed or not successful');
            return {
                isValid: false,
                amount: 0,
                fromAddress: '',
                toAddress: '',
                contractAddress: networkConfig.usdtContract,
                blockNumber: transactionInfo.blockNumber,
                blockTimestamp: transactionInfo.blockTimeStamp,
                confirmations: 0,
                transactionId: txId
            };
        }
        // Parse USDT transfer from transaction data and logs
        const transferDetails = parseUSDTTransfer(transaction, transactionInfo.log || [], networkConfig.usdtContract);
        if (!transferDetails) {
            console.log('No USDT transfer details found');
            return {
                isValid: false,
                amount: 0,
                fromAddress: '',
                toAddress: '',
                contractAddress: networkConfig.usdtContract,
                blockNumber: transactionInfo.blockNumber,
                blockTimestamp: transactionInfo.blockTimeStamp,
                confirmations: 0,
                transactionId: txId
            };
        }
        // Calculate confirmations using block numbers
        const currentBlock = await getCurrentBlock(networkConfig);
        let confirmations = 0;
        if (currentBlock && transactionInfo.blockNumber) {
            confirmations = currentBlock.blockNumber - transactionInfo.blockNumber;
        }
        console.log('Confirmation calculation:', {
            currentBlockNumber: currentBlock?.blockNumber,
            transactionBlockNumber: transactionInfo.blockNumber,
            confirmations,
            minConfirmations
        });
        // Verify the recipient address matches expected address
        const isValidRecipient = transferDetails.toAddress.toLowerCase() === expectedToAddress.toLowerCase();
        console.log('Address verification:', {
            transferToAddress: transferDetails.toAddress,
            expectedToAddress,
            isValidRecipient,
            addressesMatch: transferDetails.toAddress.toLowerCase() === expectedToAddress.toLowerCase()
        });
        const isValid = isValidRecipient && confirmations >= minConfirmations && transferDetails.amount > 0;
        console.log('Final verification result:', {
            isValid,
            isValidRecipient,
            confirmations,
            minConfirmations,
            amount: transferDetails.amount
        });
        return {
            isValid,
            amount: transferDetails.amount,
            fromAddress: transferDetails.fromAddress,
            toAddress: transferDetails.toAddress,
            contractAddress: networkConfig.usdtContract,
            blockNumber: transactionInfo.blockNumber,
            blockTimestamp: transactionInfo.blockTimeStamp,
            confirmations: Math.max(0, confirmations),
            transactionId: txId
        };
    } catch (error) {
        console.error('Error verifying USDT transaction:', error);
        return {
            isValid: false,
            amount: 0,
            fromAddress: '',
            toAddress: '',
            contractAddress: networkConfig.usdtContract,
            blockNumber: 0,
            blockTimestamp: 0,
            confirmations: 0,
            transactionId: txId
        };
    }
}
function isValidTronTransactionId(txId) {
    // Tron transaction IDs are 64-character hexadecimal strings
    const tronTxRegex = /^[a-fA-F0-9]{64}$/;
    return tronTxRegex.test(txId);
}
function isValidTronAddress(address) {
    // Tron addresses start with 'T' and are 34 characters long
    const tronAddressRegex = /^T[A-Za-z1-9]{33}$/;
    return tronAddressRegex.test(address);
}
async function getAccountInfo(address, networkConfig) {
    try {
        const data = await makeApiRequest(`/v1/accounts/${address}`, networkConfig);
        return data.data && data.data.length > 0 ? data.data[0] : null;
    } catch (error) {
        console.error('Error fetching account info:', error);
        return null;
    }
}
async function getCurrentNetworkConfig() {
    return await getTronNetworkConfig();
}
}}),
"[project]/src/lib/depositVerificationService.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DepositVerificationService": (()=>DepositVerificationService),
    "depositVerificationService": (()=>depositVerificationService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$trongrid$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/trongrid.ts [app-route] (ecmascript)");
;
;
;
// Map to track active verification processes to prevent duplicates
const activeVerifications = new Map();
// Map to track confirmation checking intervals
const confirmationIntervals = new Map();
class DepositVerificationService {
    static instance;
    isRunning = false;
    static getInstance() {
        if (!DepositVerificationService.instance) {
            DepositVerificationService.instance = new DepositVerificationService();
        }
        return DepositVerificationService.instance;
    }
    /**
   * Start the background verification service
   */ async start() {
        if (this.isRunning) {
            console.log('Deposit verification service is already running');
            return;
        }
        this.isRunning = true;
        console.log('Starting deposit verification service...');
        // Process existing pending verification deposits
        await this.processPendingVerifications();
        // Process existing waiting for confirmations deposits
        await this.processWaitingForConfirmations();
        console.log('Deposit verification service started successfully');
    }
    /**
   * Stop the background verification service
   */ stop() {
        this.isRunning = false;
        // Clear all active intervals
        confirmationIntervals.forEach((interval)=>{
            clearTimeout(interval);
        });
        confirmationIntervals.clear();
        activeVerifications.clear();
        console.log('Deposit verification service stopped');
    }
    /**
   * Process deposits with PENDING_VERIFICATION status
   */ async processPendingVerifications() {
        try {
            const pendingDeposits = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].getPendingVerificationDeposits();
            console.log(`Found ${pendingDeposits.length} deposits pending verification`);
            for (const deposit of pendingDeposits){
                if (!activeVerifications.has(deposit.transactionId)) {
                    this.scheduleVerification(deposit.transactionId, deposit.tronAddress);
                }
            }
        } catch (error) {
            console.error('Error processing pending verifications:', error);
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
                action: 'DEPOSIT_VERIFICATION_ERROR',
                details: `Error processing pending verifications: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
        }
    }
    /**
   * Process deposits with WAITING_FOR_CONFIRMATIONS status
   */ async processWaitingForConfirmations() {
        try {
            const waitingDeposits = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].getWaitingForConfirmationsDeposits();
            console.log(`Found ${waitingDeposits.length} deposits waiting for confirmations`);
            for (const deposit of waitingDeposits){
                if (!confirmationIntervals.has(deposit.transactionId)) {
                    this.scheduleConfirmationCheck(deposit.transactionId, deposit.tronAddress);
                }
            }
        } catch (error) {
            console.error('Error processing waiting for confirmations:', error);
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
                action: 'CONFIRMATION_CHECK_ERROR',
                details: `Error processing waiting for confirmations: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
        }
    }
    /**
   * Schedule verification for a transaction (with 60-second retry)
   */ scheduleVerification(transactionId, tronAddress) {
        if (activeVerifications.has(transactionId)) {
            return; // Already being processed
        }
        activeVerifications.set(transactionId, true);
        console.log(`Scheduling verification for transaction: ${transactionId}`);
        // Immediate verification attempt
        this.verifyTransaction(transactionId, tronAddress, false);
        // Schedule retry after 60 seconds if not found
        setTimeout(()=>{
            this.verifyTransaction(transactionId, tronAddress, true);
        }, 60000);
    }
    /**
   * Verify a single transaction
   */ async verifyTransaction(transactionId, tronAddress, isRetry) {
        try {
            console.log(`${isRetry ? 'Retrying' : 'Attempting'} verification for transaction: ${transactionId}`);
            // Get minimum confirmations setting
            const minConfirmations = parseInt(await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('minConfirmations') || '10');
            // Get deposit settings for validation
            const minDepositAmount = parseFloat(await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('minDepositAmount') || '10');
            const maxDepositAmount = parseFloat(await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('maxDepositAmount') || '10000');
            // Verify the transaction with timeout
            const verificationPromise = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$trongrid$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verifyUSDTTransaction"])(transactionId, tronAddress, 1);
            const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error('Verification timeout')), 30000));
            const verificationResult = await Promise.race([
                verificationPromise,
                timeoutPromise
            ]);
            if (!verificationResult.isValid && verificationResult.confirmations === 0) {
                if (isRetry) {
                    // Final attempt failed - mark as failed
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].updateStatus(transactionId, 'FAILED', {
                        failureReason: 'Transaction not found or invalid after verification attempts',
                        processedAt: new Date()
                    });
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
                        action: 'DEPOSIT_VERIFICATION_FAILED',
                        details: `Transaction ${transactionId} failed verification after retry`
                    });
                    activeVerifications.delete(transactionId);
                }
                return;
            }
            // Validate recipient address
            const hasValidRecipient = verificationResult.toAddress.toLowerCase().includes(tronAddress.toLowerCase().slice(1, 10)) || tronAddress.toLowerCase().includes(verificationResult.toAddress.toLowerCase().slice(1, 10));
            if (!hasValidRecipient) {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].updateStatus(transactionId, 'FAILED', {
                    failureReason: 'Invalid recipient address',
                    processedAt: new Date()
                });
                activeVerifications.delete(transactionId);
                return;
            }
            // Validate deposit amount
            if (verificationResult.amount < minDepositAmount) {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].updateStatus(transactionId, 'FAILED', {
                    failureReason: `Deposit amount ${verificationResult.amount} USDT is below minimum ${minDepositAmount} USDT`,
                    processedAt: new Date()
                });
                activeVerifications.delete(transactionId);
                return;
            }
            if (verificationResult.amount > maxDepositAmount) {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].updateStatus(transactionId, 'FAILED', {
                    failureReason: `Deposit amount ${verificationResult.amount} USDT exceeds maximum ${maxDepositAmount} USDT`,
                    processedAt: new Date()
                });
                activeVerifications.delete(transactionId);
                return;
            }
            // Transaction found and validated - update with verification details
            // First update the deposit record with transaction details
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.update({
                where: {
                    transactionId
                },
                data: {
                    amount: verificationResult.amount,
                    usdtAmount: verificationResult.amount,
                    senderAddress: verificationResult.fromAddress,
                    blockNumber: verificationResult.blockNumber.toString(),
                    blockTimestamp: new Date(verificationResult.blockTimestamp),
                    confirmations: verificationResult.confirmations
                }
            });
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].updateStatus(transactionId, 'PENDING', {
                confirmations: verificationResult.confirmations
            });
            console.log(`Transaction ${transactionId} verified with ${verificationResult.confirmations} confirmations (required: ${minConfirmations})`);
            // Check if it has enough confirmations
            if (verificationResult.confirmations >= minConfirmations) {
                await this.completeDeposit(transactionId, verificationResult.amount);
            } else {
                // Not enough confirmations - start confirmation checking
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].updateStatus(transactionId, 'WAITING_FOR_CONFIRMATIONS');
                this.scheduleConfirmationCheck(transactionId, tronAddress);
            }
            activeVerifications.delete(transactionId);
        } catch (error) {
            console.error(`Error verifying transaction ${transactionId}:`, error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const isNetworkError = errorMessage.includes('timeout') || errorMessage.includes('network') || errorMessage.includes('ECONNRESET');
            if (isRetry || !isNetworkError) {
                // Final attempt failed due to error or non-network error
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].updateStatus(transactionId, 'FAILED', {
                    failureReason: `Verification error: ${errorMessage}`,
                    processedAt: new Date()
                });
                activeVerifications.delete(transactionId);
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
                    action: 'DEPOSIT_VERIFICATION_FAILED',
                    details: `Transaction ${transactionId} failed verification: ${errorMessage}`
                });
            } else {
                // Network error on first attempt - will retry in 60 seconds
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
                    action: 'DEPOSIT_VERIFICATION_NETWORK_ERROR',
                    details: `Network error verifying transaction ${transactionId}: ${errorMessage}. Will retry.`
                });
            }
        }
    }
    /**
   * Schedule confirmation checking for a transaction
   */ scheduleConfirmationCheck(transactionId, tronAddress) {
        if (confirmationIntervals.has(transactionId)) {
            return; // Already being checked
        }
        console.log(`Starting confirmation checking for transaction: ${transactionId}`);
        const interval = setInterval(async ()=>{
            await this.checkConfirmations(transactionId, tronAddress);
        }, 60000); // Check every 60 seconds
        confirmationIntervals.set(transactionId, interval);
        // Also check immediately
        this.checkConfirmations(transactionId, tronAddress);
    }
    /**
   * Check confirmations for a transaction
   */ async checkConfirmations(transactionId, tronAddress) {
        try {
            console.log(`Checking confirmations for transaction: ${transactionId}`);
            // Get minimum confirmations setting
            const minConfirmations = parseInt(await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('minConfirmations') || '10');
            // Re-verify to get current confirmation count
            const verificationResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$trongrid$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verifyUSDTTransaction"])(transactionId, tronAddress, 1);
            if (!verificationResult.isValid) {
                console.log(`Transaction ${transactionId} is no longer valid during confirmation check`);
                return;
            }
            // Update confirmation count
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].updateConfirmations(transactionId, verificationResult.confirmations);
            console.log(`Transaction ${transactionId} has ${verificationResult.confirmations} confirmations (required: ${minConfirmations})`);
            // Check if it now has enough confirmations
            if (verificationResult.confirmations >= minConfirmations) {
                await this.completeDeposit(transactionId, verificationResult.amount);
                // Stop checking confirmations for this transaction
                const interval = confirmationIntervals.get(transactionId);
                if (interval) {
                    clearInterval(interval);
                    confirmationIntervals.delete(transactionId);
                }
            }
        } catch (error) {
            console.error(`Error checking confirmations for transaction ${transactionId}:`, error);
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
                action: 'CONFIRMATION_CHECK_ERROR',
                details: `Error checking confirmations for ${transactionId}: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
        }
    }
    /**
   * Complete a deposit by crediting the user's wallet
   */ async completeDeposit(transactionId, amount) {
        try {
            console.log(`Completing deposit for transaction: ${transactionId} with amount: ${amount}`);
            // Get deposit record to get user ID
            const deposit = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].findByTransactionId(transactionId);
            if (!deposit) {
                throw new Error('Deposit record not found');
            }
            // Check if deposit is already completed to prevent double processing
            if (deposit.status === 'CONFIRMED' || deposit.status === 'COMPLETED') {
                console.log(`Deposit ${transactionId} already completed, skipping...`);
                return;
            }
            // Use database transaction to ensure atomicity
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
                // Update deposit status to confirmed
                await tx.depositTransaction.update({
                    where: {
                        transactionId
                    },
                    data: {
                        status: 'CONFIRMED',
                        verifiedAt: new Date(),
                        processedAt: new Date()
                    }
                });
                // Get current wallet balance
                const currentWallet = await tx.walletBalance.findUnique({
                    where: {
                        userId: deposit.userId
                    }
                });
                if (!currentWallet) {
                    // Create wallet if it doesn't exist
                    await tx.walletBalance.create({
                        data: {
                            userId: deposit.userId,
                            availableBalance: amount,
                            pendingBalance: 0,
                            totalDeposits: amount,
                            totalWithdrawals: 0,
                            totalEarnings: 0
                        }
                    });
                } else {
                    // Update existing wallet
                    await tx.walletBalance.update({
                        where: {
                            userId: deposit.userId
                        },
                        data: {
                            availableBalance: currentWallet.availableBalance + amount,
                            totalDeposits: currentWallet.totalDeposits + amount,
                            lastUpdated: new Date()
                        }
                    });
                }
                // Create transaction record for balance tracking
                await tx.transaction.create({
                    data: {
                        userId: deposit.userId,
                        type: 'DEPOSIT',
                        amount: amount,
                        description: `USDT TRC20 Deposit - TX: ${transactionId}`,
                        status: 'COMPLETED'
                    }
                });
            });
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
                action: 'DEPOSIT_COMPLETED',
                userId: deposit.userId,
                details: `Deposit completed: ${amount} USDT from transaction ${transactionId}`
            });
            console.log(`Deposit completed successfully for transaction: ${transactionId}`);
        } catch (error) {
            console.error(`Error completing deposit for transaction ${transactionId}:`, error);
            // Mark as failed if completion fails
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].updateStatus(transactionId, 'FAILED', {
                failureReason: `Completion error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                processedAt: new Date()
            });
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
                action: 'DEPOSIT_COMPLETION_ERROR',
                details: `Error completing deposit ${transactionId}: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
        }
    }
    /**
   * Add a new transaction for verification
   */ async addTransactionForVerification(transactionId, tronAddress) {
        if (!this.isRunning) {
            console.log('Deposit verification service is not running, starting verification manually');
        }
        this.scheduleVerification(transactionId, tronAddress);
    }
    /**
   * Get service status
   */ getStatus() {
        return {
            isRunning: this.isRunning,
            activeVerifications: activeVerifications.size,
            confirmationChecks: confirmationIntervals.size
        };
    }
}
const depositVerificationService = DepositVerificationService.getInstance();
}}),
"[project]/src/app/api/wallet/deposit/verify/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$trongrid$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/trongrid.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$depositVerificationService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/depositVerificationService.ts [app-route] (ecmascript)");
;
;
;
;
;
// Rate limiting map to prevent abuse
const rateLimitMap = new Map();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 5; // 5 requests per minute per user
function checkRateLimit(userId) {
    const now = Date.now();
    const userLimit = rateLimitMap.get(userId);
    if (!userLimit || now > userLimit.resetTime) {
        rateLimitMap.set(userId, {
            count: 1,
            resetTime: now + RATE_LIMIT_WINDOW
        });
        return true;
    }
    if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
        return false;
    }
    userLimit.count++;
    return true;
}
async function POST(request) {
    try {
        const { authenticated, user } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authenticateRequest"])(request);
        if (!authenticated || !user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Not authenticated'
            }, {
                status: 401
            });
        }
        // Check rate limiting
        if (!checkRateLimit(user.id)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Too many verification requests. Please wait before trying again.'
            }, {
                status: 429
            });
        }
        const body = await request.json();
        const { transactionId } = body;
        // Validation
        if (!transactionId || typeof transactionId !== 'string') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Transaction ID is required'
            }, {
                status: 400
            });
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$trongrid$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isValidTronTransactionId"])(transactionId)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Invalid Tron transaction ID format'
            }, {
                status: 400
            });
        }
        // Check if transaction already exists
        const existingDeposit = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].findByTransactionId(transactionId);
        if (existingDeposit) {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
                action: 'DEPOSIT_DUPLICATE_ATTEMPT',
                userId: user.id,
                details: {
                    transactionId,
                    existingDepositId: existingDeposit.id,
                    existingStatus: existingDeposit.status
                },
                ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
                userAgent: request.headers.get('user-agent') || 'unknown'
            });
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'This transaction ID has already been submitted. Please check your deposit history.',
                data: {
                    existingStatus: existingDeposit.status,
                    submittedAt: existingDeposit.createdAt
                }
            }, {
                status: 400
            });
        }
        // Get deposit settings - try both camelCase and UPPER_CASE keys for compatibility
        let depositAddress = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('usdtDepositAddress');
        if (!depositAddress) {
            depositAddress = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('USDT_DEPOSIT_ADDRESS');
        }
        // Clean deposit address - remove quotes and extra characters
        if (depositAddress) {
            depositAddress = depositAddress.replace(/['"]/g, '').trim();
        }
        let minDepositAmount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('minDepositAmount');
        if (!minDepositAmount) {
            minDepositAmount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('MIN_DEPOSIT_AMOUNT');
        }
        minDepositAmount = parseFloat(minDepositAmount || '10');
        let maxDepositAmount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('maxDepositAmount');
        if (!maxDepositAmount) {
            maxDepositAmount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('MAX_DEPOSIT_AMOUNT');
        }
        maxDepositAmount = parseFloat(maxDepositAmount || '10000');
        let depositEnabled = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('depositEnabled');
        if (!depositEnabled) {
            depositEnabled = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('DEPOSIT_ENABLED');
        }
        depositEnabled = depositEnabled === 'true' || depositEnabled === true;
        let minConfirmations = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('minConfirmations');
        if (!minConfirmations) {
            minConfirmations = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('MIN_CONFIRMATIONS');
        }
        minConfirmations = parseInt(minConfirmations || '1');
        if (!depositEnabled) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Deposits are currently disabled'
            }, {
                status: 503
            });
        }
        if (!depositAddress) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Deposit address not configured. Please contact support.'
            }, {
                status: 503
            });
        }
        // Log verification attempt
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
            action: 'DEPOSIT_VERIFICATION_ATTEMPT',
            userId: user.id,
            details: {
                transactionId
            },
            ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown'
        });
        // Immediately create deposit record with PENDING_VERIFICATION status
        // This happens regardless of transaction validity
        const depositRecord = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].create({
            userId: user.id,
            transactionId,
            amount: 0,
            usdtAmount: 0,
            tronAddress: depositAddress,
            senderAddress: '',
            blockNumber: '',
            blockTimestamp: new Date(),
            confirmations: 0
        });
        // Update status to PENDING_VERIFICATION
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositTransactionDb"].updateStatus(transactionId, 'PENDING_VERIFICATION');
        // Start background verification process
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$depositVerificationService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["depositVerificationService"].addTransactionForVerification(transactionId, depositAddress);
        // Log deposit submission
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
            action: 'DEPOSIT_SUBMITTED',
            userId: user.id,
            details: {
                transactionId,
                depositAddress,
                status: 'PENDING_VERIFICATION'
            },
            ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown'
        });
        // Return immediate success response
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Deposit transaction submitted successfully. We are now verifying your transaction. This may take up to 2 minutes.',
            data: {
                transactionId,
                status: 'PENDING_VERIFICATION',
                estimatedVerificationTime: 'Within 2 minutes',
                nextSteps: [
                    'Transaction verification in progress',
                    'Confirmation checking will begin once transaction is found',
                    `Wallet will be credited automatically after ${minConfirmations} confirmations`
                ]
            }
        });
    } catch (error) {
        console.error('Deposit verification error:', error);
        // Log error for debugging
        if (request.headers.get('authorization')) {
            try {
                const { user } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authenticateRequest"])(request);
                if (user) {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
                        action: 'DEPOSIT_VERIFICATION_ERROR',
                        userId: user.id,
                        details: {
                            error: error instanceof Error ? error.message : 'Unknown error'
                        },
                        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
                        userAgent: request.headers.get('user-agent') || 'unknown'
                    });
                }
            } catch (logError) {
                console.error('Failed to log error:', logError);
            }
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to verify deposit. Please try again later.'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__4f2872f5._.js.map